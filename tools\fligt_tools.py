

from langchain.tools import tool
import requests

# Flight Agent Tools
@tool
def flight_search(destination: str, date: str):
    """Search flights to a destination on a specific date."""
    response = requests.post("http://yourapi.com/flight/search", json={
        "destination": destination,
        "date": date
    })
    return response.json()

@tool
def flight_book(flight_id: str, user_id: str):
    """Book a flight using flight ID."""
    response = requests.post("http://yourapi.com/flight/book", json={
        "flight_id": flight_id,
        "user_id": user_id
    })
    return response.json()
