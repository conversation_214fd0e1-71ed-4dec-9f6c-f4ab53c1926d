from typing import Dict, List, TypedDict
from pydantic import BaseModel, Field
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.runnables import Runnable
from langchain_core.runnables.graph import StateGraph
from langchain_core.runnables.utils import END

# 1. Define System State
class State(TypedDict):
    query: str
    tools: Dict[str, List[str]]
    response: List[str]

# 2. Define Output Schema for Tool Routing
class ToolRouting(BaseModel):
    tools: Dict[str, List[str]] = Field(
        description="Dictionary mapping agents to the specific tools to be called for the query"
    )

# 3. Routing logic (LLM-powered classifier)
def router(state: State):
    system_prompt = """
You are a classifier that receives a user query and selects the appropriate tools for the task.

Agents and their tools are:

- FLIGHT_AGENT: ["flight_search", "flight_book", "flight_cancel"]
- HOTEL_AGENT: ["hotel_search", "hotel_book", "hotel_cancel"]
- RECOMMENDATION_AGENT: ["sightseeing_tools", "itinerary_tools"]
- POLICY_AGENT: ["policy_check", "policy_update"]
- RISK_AGENT: ["risk_alert"]

Return a dictionary mapping each agent to a list of tools needed to fulfill the query.

Example:
{
  "tools": {
    "FLIGHT_AGENT": ["flight_book"],
    "HOTEL_AGENT": ["hotel_book"],
    "RECOMMENDATION_AGENT": ["sightseeing_tools"]
  }
}
Only include the relevant agents and tools.
"""
    router_chain: Runnable = llm.with_structured_output(ToolRouting)
    response = router_chain.invoke([
        SystemMessage(content=system_prompt),
        HumanMessage(content=state["query"])
    ])
    print("Router response:", response)
    return {"tools": response.tools}

# 4. Router output mapper
def route_to_agents(state: State) -> List[str]:
    return list(state["tools"].keys())