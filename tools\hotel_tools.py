

from langchain.tools import tool
import requests

# Flight Agent Tools
@tool
def hotel_search(location: str, date: str):
    """Search flights to a destination on a specific date."""
    response = requests.post("http://yourapi.com/hotel/search", json={
        "location": location,
        "date": date
    })
    return response.json()

@tool
def hotel_book(hotel_id: str, user_id: str):
    """Book a flight using flight ID."""
    response = requests.post("http://yourapi.com/hotel/book", json={
        "hotel_id": hotel_id,
        "user_id": user_id
    })
    return response.json()
